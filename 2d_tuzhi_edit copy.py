# -*- coding: utf-8 -*-
import MySQL
import Lite
from Function import *
import configparser




config = configparser.ConfigParser()
try:
    cfgfile = open('config.ini', 'r')
    config.readfp(cfgfile)
    zip_db_host = config.get('zip', 'zip_db_host')
    zip_user = config.get('zip', 'zip_db_user')
    zip_db_password = config.get('zip', 'zip_db_password')
    zip_db_db = config.get('zip', 'zip_db_db')

    tuzhi_db_host = config.get('tuzhi', 'tuzhi_db_host')
    tuzhi_user = config.get('tuzhi', 'tuzhi_db_user')
    tuzhi_db_password = config.get('tuzhi', 'tuzhi_db_password')
    tuzhi_db_db = config.get('tuzhi', 'tuzhi_db_db')
    # 临时解压目录，谨慎填写，否则可能会删除正常文件
    zipTempDir = config.get('path', 'zipTempDir')
    downDir = config.get('path', 'downDir')

    access_key = config.get('qiniu', 'access_key')
    secret_key = config.get('qiniu', 'secret_key')
    bucket_name = config.get('qiniu', 'bucket_name')
    bucket_domain = config.get('qiniu', 'bucket_domain')

    cos_access_key = config.get('cos', 'access_key')
    cos_secret_key = config.get('cos', 'secret_key')
    cos_bucket_name = config.get('cos', 'bucket_name')
except:
    print('读取配置文件错误！')
    exit()

def mysqls():
    n = MySQL.MySQL(tuzhi_db_host, tuzhi_user, tuzhi_db_password, 3306)
    n.selectDb(tuzhi_db_db)
    return n

def zip_mysqls():
    n = MySQL.MySQL(zip_db_host, zip_user, zip_db_password, 3306)
    n.selectDb(zip_db_db)
    return n

def mfcad_Lite():
    mfcad_lite = Lite.Lite()
    return mfcad_lite


#修改图纸数据表字段状态
def update_isedrawings(status, tuid):
    n = mysqls()
    n.update('v9_dede_soft', {'isedrawings': status}, 'id=' + str(tuid))
    n.commit()

#添加轻量化数据
def update_task(data, tuid):
    z = zip_mysqls()
    for d in data:
        liteurl_array = base64.b64decode(d['outputNdsFiles']).decode('utf-8').split('?X-Amz-Algorithm')[0].split('/')
        liteurl = 'D:/3DLite/minioSever/severData/openapi/tempFile/download/'+ liteurl_array[-2]+'/'+liteurl_array[-1]
        d['outputNdsFiles'] = '2025_5_10/'+str(tuid)+'/'+str(d['taskId'])+'/'
        file_result = cos_unzip_upload(cos_access_key,cos_secret_key,cos_bucket_name, zipTempDir, liteurl, d['outputNdsFiles'])
        if file_result == False:
            d['outputNdsFiles'] = '上传失败,请检查程序是否异常!'

        imgurl = 'D:/3DLite/minioSever/severData/openapi/' + base64.b64decode(d['imgURL']).decode('utf-8').split('/openapi/')[-1]
        d['imgURL'] = '2025_5_10//'+str(tuid)+'/'+str(d['taskId'])+'/'+str(d['taskId'])+'.png'
        file_result = cos_upload(cos_access_key,cos_secret_key,cos_bucket_name,imgurl, d['imgURL'])
        if file_result == False:
            d['imgURL'] = '上传失败,请检查程序是否异常!'


        z.update('3d_taskdetail',{'inputFileName':d['inputFileName'],'convertTime':d['convertTime'],'inputFileSize':d['inputFileSize'],'outputFileSize':d['outputFileSize'],'convertPercent':d['convertPercent'],'triangleNum':d['triangleNum'],'taskType':d['taskType'],'status':d['status'],'imgURL':d['imgURL'],'outputNdsFiles':d['outputNdsFiles'],'errorDescription':d['errorDescription'],'errorDetail':d['errorDetail'],'errorType':d['errorType']}, 'taskid = ' + str(d['taskId']))
    z.commit()



# 列出符合条件的所有文件，并下载
def listAll(bucket_name):
    n = mysqls()
    z = zip_mysqls()
    mfcad_lite = mfcad_Lite()
    #3天前的时间戳 只处理3天以内的数据
    maxtime = get_unixtime()-(86400*3)
    z.query('SELECT id,GROUP_CONCAT(taskid) as taskid,tuid,`status` FROM `3d_taskdetail` where status = 1 and addtime >= '+ str(maxtime)+' group by tuid order by id asc limit 10')
    tasklist = z.fetchAll()
    
    for tudata in tasklist:
        tasks_success = 3
        all_task_details = []  # 存储所有任务的详细信息
        # 循环处理每个任务id，等待所有任务完成
        taskids = tudata['taskid'].split(',')
        #重试次数
        num = 0
        for taskid in taskids:
            while True:
                if num > 10:
                    z.query('SELECT id, taskid, tuid, num FROM `3d_taskerror` where tuid = ' + str(tudata['tuid']) + ' and taskid = ' + str(taskid))
                    maxtaskinfo = z.fetchAll()
                    if maxtaskinfo:
                        if maxtaskinfo['num'] >= 80:
                            
        z.update('3d_taskdetail',{'inputFileName':d['inputFileName'],'convertTime':d['convertTime'],'inputFileSize':d['inputFileSize'],'outputFileSize':d['outputFileSize'],'convertPercent':d['convertPercent'],'triangleNum':d['triangleNum'],'taskType':d['taskType'],'status':d['status'],'imgURL':d['imgURL'],'outputNdsFiles':d['outputNdsFiles'],'errorDescription':d['errorDescription'],'errorDetail':d['errorDetail'],'errorType':d['errorType']}, 'taskid = ' + str(d['taskId']))
    z.commit()
                            break
                        else:
                            z.update('3d_taskerror', {'num': maxtaskinfo['num'] + 10}, 'id = ' + str(maxtaskinfo['id']))
                            z.commit()
                    else:
                        z.insert('3d_taskerror', {'tuid': tudata['tuid'], 'taskid': taskid, 'num': 10, 'addtime': get_unixtime(), 'days': get_days()})
                        z.commit()
        

                        

                    break
                taskinfo = mfcad_lite.QueryTaskDetail(taskid)
                taskinfo['data']['taskId'] = taskid
                if int(taskinfo['data']['status']) < 2 and num < 10:
                    num = num + 1
                    print(f'任务{taskid}转换中,继续等待2秒进行下次轮询...')
                    time.sleep(2)
                    continue
                #转换成功后 imgURL 返回有延迟 需要等待返回后在执行
                elif int(taskinfo['data']['status']) == 2  and taskinfo['data']['imgURL'] != '':
                    print(f'任务{taskid}转换成功')
                    tasks_success = 2  # 任何一个任务成功，整体标记为成功
                    all_task_details.append(taskinfo['data'])
                    break
                elif int(taskinfo['data']['status']) > 3:
                    print(f'任务{taskid}转换失败，状态码：{taskinfo["data"]["status"]}')
                    all_task_details.append(taskinfo['data'])
                    break
                else:
                    num = num + 1
                    print(f'任务{taskid}转换成功，但imgURL未返回，继续等待8秒进行下次轮询...')
                    time.sleep(8)
                    continue

        # 所有任务处理完成后，统一更新数据库状态
        print(f'所有任务处理完成，最终状态：{tasks_success}')
        update_isedrawings(tasks_success, tudata['tuid'])
        
        # 更新所有任务的详细信息
        update_task(all_task_details, tudata['tuid'])



if __name__ == '__main__':
    while True:
        listAll(bucket_name)
        print('30秒钟后重新开始扫描！')
        time.sleep(30)
