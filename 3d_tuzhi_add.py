# -*- coding: utf-8 -*-
import MySQL
import Lite
from Function import *
import configparser




config = configparser.ConfigParser()
try:
    cfgfile = open('config.ini', 'r')
    config.readfp(cfgfile)
    zip_db_host = config.get('zip', 'zip_db_host')
    zip_user = config.get('zip', 'zip_db_user')
    zip_db_password = config.get('zip', 'zip_db_password')
    zip_db_db = config.get('zip', 'zip_db_db')

    tuzhi_db_host = config.get('tuzhi', 'tuzhi_db_host')
    tuzhi_user = config.get('tuzhi', 'tuzhi_db_user')
    tuzhi_db_password = config.get('tuzhi', 'tuzhi_db_password')
    tuzhi_db_db = config.get('tuzhi', 'tuzhi_db_db')
    # 临时解压目录，谨慎填写，否则可能会删除正常文件
    zipTempDir = config.get('path', 'zipTempDir')
    downDir = config.get('path', 'downDir')

    access_key = config.get('qiniu', 'access_key')
    secret_key = config.get('qiniu', 'secret_key')
    bucket_name = config.get('qiniu', 'bucket_name')
    bucket_domain = config.get('qiniu', 'bucket_domain')

    cos_access_key = config.get('cos', 'access_key')
    cos_secret_key = config.get('cos', 'secret_key')
    cos_bucket_name = config.get('cos', 'bucket_name')
except:
    print('读取配置文件错误！')
    exit()

def mysqls():
    n = MySQL.MySQL(tuzhi_db_host, tuzhi_user, tuzhi_db_password, 3306)
    n.selectDb(tuzhi_db_db)
    return n

def zip_mysqls():
    n = MySQL.MySQL(zip_db_host, zip_user, zip_db_password, 3306)
    n.selectDb(zip_db_db)
    return n

def mfcad_Lite():
    mfcad_lite = Lite.Lite()
    return mfcad_lite


#修改图纸数据表字段状态
def update_isedrawings(n, status, tuid):
    n.update('v9_dede_soft', {'isedrawings': status}, 'id=' + str(tuid))
    n.commit()

# 列出符合条件的所有文件，并下载
def listAll():
    n = mysqls()
    z = zip_mysqls()
    mfcad_lite = mfcad_Lite()

    # n.query('SELECT s.id AS tuid,REPLACE (d.test, "tuzhi/", "") AS filename FROM v9_dede_soft s INNER JOIN v9_dede_soft_data d ON s.id = d.id WHERE s.status = 1 and s.format_type = 2 and s.isedrawings = 0 and d.test <> "" order by s.id asc limit 1')
    n.query('SELECT s.id AS tuid,REPLACE (d.test, "tuzhi/", "") AS filename FROM v9_dede_soft s INNER JOIN v9_dede_soft_data d ON s.id = d.id WHERE s.id = 1759961')
    tulist = n.fetchAll()
    if not tulist or len(tulist[0]['filename']) == 0:
        print('二维图纸没有待转换任务!!!')
        return
    
    for tudata in tulist:
        update_isedrawings(n, 1, tudata['tuid'])
        tuzhi_info = get_file_from_qiniu(access_key, secret_key, bucket_domain, downDir, bucket_name, tudata['filename'], tudata['tuid'])
        if not tuzhi_info['file_status']:
            update_isedrawings(n, 3, tudata['tuid'])
            continue
        
        batch = True
        #判断文件名是否是压缩包
        if iszip(tudata['filename']):
            datainfo =mfcad_lite.BatchInsertTask(tuzhi_info['tuzhi_url'])
        else:
            batch = False
            datainfo =mfcad_lite.InsertTask(tuzhi_info['tuzhi_url'])
            print(datainfo)
            z.insert('3d_taskdetail',{'tuid':tudata['tuid'], 'status': 1,'taskid':datainfo['data']['taskID'], 'addtime': get_unixtime(), 'days':get_days()})
            z.commit()

        if batch:
            batchinfo = mfcad_lite.QueryBatchTask(datainfo['data']['batchNO'])
            batch_status = True
            while batch_status:
                if int(batchinfo['data']['status']) < 2:
                    print(f'批次{datainfo["data"]["batchNO"]}转换中,继续等待5秒进行下次轮询...')
                    time.sleep(5)
                    batchinfo = mfcad_lite.QueryBatchTask(datainfo['data']['batchNO'])
                    continue
                else:
                    batch_status = False
                    print(batchinfo)
                    try:
                        for d in batchinfo['data']['childList']:
                            z.insert('3d_taskdetail',{'tuid':tudata['tuid'], 'status': 1,'taskid':d['taskId'], 'addtime': get_unixtime(), 'days':get_days()})
                        z.commit()
                    except:
                        update_isedrawings(n, 3, tudata['tuid'])
        if os.path.exists(downDir + '/' + tudata['filename']) and get_month() not in tudata['filename']:
            shutil.rmtree(downDir + '/' + tudata['filename'])
        

if __name__ == '__main__':
    while True:
        listAll()
        print('30秒钟后重新开始扫描!!!!!!!!!!!!!！')
        time.sleep(30)
