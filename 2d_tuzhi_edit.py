# -*- coding: utf-8 -*-
import MySQL
import Lite
from Function import *
import configparser




config = configparser.ConfigParser()
try:
    cfgfile = open('config.ini', 'r')
    config.readfp(cfgfile)
    zip_db_host = config.get('zip', 'zip_db_host')
    zip_user = config.get('zip', 'zip_db_user')
    zip_db_password = config.get('zip', 'zip_db_password')
    zip_db_db = config.get('zip', 'zip_db_db')

    tuzhi_db_host = config.get('tuzhi', 'tuzhi_db_host')
    tuzhi_user = config.get('tuzhi', 'tuzhi_db_user')
    tuzhi_db_password = config.get('tuzhi', 'tuzhi_db_password')
    tuzhi_db_db = config.get('tuzhi', 'tuzhi_db_db')
    # 临时解压目录，谨慎填写，否则可能会删除正常文件
    zipTempDir = config.get('path', 'zipTempDir')
    downDir = config.get('path', 'downDir')

    access_key = config.get('qiniu', 'access_key')
    secret_key = config.get('qiniu', 'secret_key')
    bucket_name = config.get('qiniu', 'bucket_name')
    bucket_domain = config.get('qiniu', 'bucket_domain')

    cos_access_key = config.get('cos', 'access_key')
    cos_secret_key = config.get('cos', 'secret_key')
    cos_bucket_name = config.get('cos', 'bucket_name')
except:
    print('读取配置文件错误！')
    exit()

def mysqls():
    n = MySQL.MySQL(tuzhi_db_host, tuzhi_user, tuzhi_db_password, 3306)
    n.selectDb(tuzhi_db_db)
    return n

def zip_mysqls():
    n = MySQL.MySQL(zip_db_host, zip_user, zip_db_password, 3306)
    n.selectDb(zip_db_db)
    return n

def mfcad_Lite():
    mfcad_lite = Lite.Lite()
    return mfcad_lite


#修改图纸数据表字段状态
def update_isedrawings(status, tuid):
    n = mysqls()
    n.update('v9_dede_soft', {'isedrawings': status}, 'id=' + str(tuid))
    n.commit()

#添加轻量化数据
def update_task(data, tuid):
    z = zip_mysqls()
    for d in data:
        liteurl_array = base64.b64decode(d['outputNdsFiles']).decode('utf-8').split('?X-Amz-Algorithm')[0].split('/')
        liteurl = 'D:/3DLite/minioSever/severData/openapi/tempFile/download/'+ liteurl_array[-2]+'/'+liteurl_array[-1]
        d['outputNdsFiles'] = '2025_5_10/'+str(tuid)+'/'+str(d['taskId'])+'/'
        file_result = cos_unzip_upload(cos_access_key,cos_secret_key,cos_bucket_name, zipTempDir, liteurl, d['outputNdsFiles'])
        if file_result == False:
            d['outputNdsFiles'] = '上传失败,请检查程序是否异常!'

        imgurl = 'D:/3DLite/minioSever/severData/openapi/' + base64.b64decode(d['imgURL']).decode('utf-8').split('/openapi/')[-1]
        d['imgURL'] = '2025_5_10//'+str(tuid)+'/'+str(d['taskId'])+'/'+str(d['taskId'])+'.png'
        file_result = cos_upload(cos_access_key,cos_secret_key,cos_bucket_name,imgurl, d['imgURL'])
        if file_result == False:
            d['imgURL'] = '上传失败,请检查程序是否异常!'


        z.update('3d_taskdetail',{'inputFileName':d['inputFileName'],'convertTime':d['convertTime'],'inputFileSize':d['inputFileSize'],'outputFileSize':d['outputFileSize'],'convertPercent':d['convertPercent'],'triangleNum':d['triangleNum'],'taskType':d['taskType'],'status':d['status'],'imgURL':d['imgURL'],'outputNdsFiles':d['outputNdsFiles'],'errorDescription':d['errorDescription'],'errorDetail':d['errorDetail'],'errorType':d['errorType']}, 'taskid = ' + str(d['taskId']))
    z.commit()



# 全局变量：跟踪转换中的任务组
processing_task_groups = {}

# 处理单个任务组
def process_task_group(tudata, mfcad_lite):
    """处理单个任务组，返回处理结果"""
    tasks_success = 3
    all_task_details = []
    taskids = tudata['taskid'].split(',')

    print(f'检查tuid={tudata["tuid"]}的任务组，包含{len(taskids)}个任务')

    # 快速检查所有任务状态
    completed_tasks = []
    processing_tasks = []

    for taskid in taskids:
        try:
            taskinfo = mfcad_lite.QueryTaskDetail(taskid)
            taskinfo['data']['taskId'] = taskid
            task_status = int(taskinfo['data']['status'])

            if task_status < 2:
                processing_tasks.append({
                    'taskid': taskid,
                    'status': task_status
                })
            elif task_status == 2 and taskinfo['data']['imgURL'] != '':
                completed_tasks.append(taskinfo['data'])
                tasks_success = 2
            elif task_status == 2 and taskinfo['data']['imgURL'] == '':
                processing_tasks.append({
                    'taskid': taskid,
                    'status': task_status,
                    'waiting_img': True
                })
            elif task_status > 3:
                completed_tasks.append(taskinfo['data'])
            else:
                processing_tasks.append({
                    'taskid': taskid,
                    'status': task_status
                })
        except Exception as e:
            print(f'查询任务{taskid}状态失败：{e}')
            continue

    # 立即处理已完成的任务
    all_task_details.extend(completed_tasks)

    return {
        'tuid': tudata['tuid'],
        'tasks_success': tasks_success,
        'completed_tasks': all_task_details,
        'processing_tasks': processing_tasks,
        'is_fully_completed': len(processing_tasks) == 0
    }

# 更新任务组状态到数据库
def update_task_group_status(result):
    """更新任务组状态到数据库"""
    if result['completed_tasks']:
        print(f'更新tuid={result["tuid"]}的{len(result["completed_tasks"])}个已完成任务到数据库')
        update_isedrawings(result['tasks_success'], result['tuid'])
        update_task(result['completed_tasks'], result['tuid'])
        return True
    return False

# 列出符合条件的所有文件，并下载
def listAll(bucket_name):
    global processing_task_groups

    n = mysqls()
    z = zip_mysqls()
    mfcad_lite = mfcad_Lite()
    #3天前的时间戳 只处理3天以内的数据
    maxtime = get_unixtime()-(86400*3)
    z.query('SELECT id,GROUP_CONCAT(taskid) as taskid,tuid,`status` FROM `3d_taskdetail` where status = 1 and addtime >= '+ str(maxtime)+' group by tuid order by id asc limit 10')
    tasklist = z.fetchAll()

    print(f'发现{len(tasklist)}个任务组待处理')

    # 第一阶段：快速处理所有任务组，分离已完成和转换中的任务组
    fully_completed_groups = []
    partially_completed_groups = []

    for tudata in tasklist:
        # 快速检查任务组状态
        result = process_task_group(tudata, mfcad_lite)

        if result['is_fully_completed']:
            # 完全完成的任务组立即处理
            fully_completed_groups.append(result)
            print(f'任务组tuid={result["tuid"]}已完全完成，加入立即处理队列')
        else:
            # 部分完成的任务组
            partially_completed_groups.append(result)
            # 将转换中的任务组加入全局跟踪
            processing_task_groups[result['tuid']] = {
                'result': result,
                'last_check': get_unixtime(),
                'check_count': 0
            }
            print(f'任务组tuid={result["tuid"]}部分完成，加入跟踪队列')

    # 第二阶段：立即处理完全完成的任务组
    completed_count = 0
    for result in fully_completed_groups:
        if update_task_group_status(result):
            completed_count += 1

    # 第三阶段：处理部分完成任务组中的已完成任务
    partial_completed_count = 0
    for result in partially_completed_groups:
        if result['completed_tasks']:  # 如果有已完成的任务，先处理这些
            if update_task_group_status(result):
                partial_completed_count += 1

    print(f'本轮处理完成：{completed_count}个完整任务组，{partial_completed_count}个部分任务组')

    # 第四阶段：检查之前跟踪的转换中任务组
    check_processing_task_groups(mfcad_lite)

def check_processing_task_groups(mfcad_lite):
    """检查跟踪中的转换任务组"""
    global processing_task_groups

    if not processing_task_groups:
        return

    print(f'检查{len(processing_task_groups)}个跟踪中的任务组')
    completed_tuids = []

    for tuid, tracking_info in processing_task_groups.items():
        current_time = get_unixtime()
        # 避免频繁检查，至少间隔10秒
        if current_time - tracking_info['last_check'] < 10:
            continue

        tracking_info['last_check'] = current_time
        tracking_info['check_count'] += 1

        # 超过30次检查(约5分钟)后放弃跟踪
        if tracking_info['check_count'] > 30:
            print(f'任务组tuid={tuid}跟踪超时，停止跟踪')
            completed_tuids.append(tuid)
            continue

        print(f'检查跟踪任务组tuid={tuid}，第{tracking_info["check_count"]}次检查')

        # 重新检查任务组状态
        tudata = {'tuid': tuid, 'taskid': ','.join([task['taskid'] for task in tracking_info['result']['processing_tasks']])}
        new_result = process_task_group(tudata, mfcad_lite)

        if new_result['is_fully_completed']:
            # 任务组完成，更新数据库并移除跟踪
            print(f'跟踪任务组tuid={tuid}已完成')
            update_task_group_status(new_result)
            completed_tuids.append(tuid)
        elif new_result['completed_tasks']:
            # 有新的已完成任务，更新数据库但继续跟踪
            print(f'跟踪任务组tuid={tuid}有新完成任务')
            update_task_group_status(new_result)
            tracking_info['result'] = new_result

    # 移除已完成的跟踪任务组
    for tuid in completed_tuids:
        del processing_task_groups[tuid]

    if completed_tuids:
        print(f'移除{len(completed_tuids)}个已完成的跟踪任务组')



if __name__ == '__main__':
    while True:
        listAll(bucket_name)
        print('30秒钟后重新开始扫描！')
        time.sleep(30)
