# -*- coding: utf-8 -*-
import MySQL
import Lite
from Function import *
import configparser




config = configparser.ConfigParser()
try:
    cfgfile = open('config.ini', 'r')
    config.readfp(cfgfile)
    zip_db_host = config.get('zip', 'zip_db_host')
    zip_user = config.get('zip', 'zip_db_user')
    zip_db_password = config.get('zip', 'zip_db_password')
    zip_db_db = config.get('zip', 'zip_db_db')

    tuzhi_db_host = config.get('tuzhi', 'tuzhi_db_host')
    tuzhi_user = config.get('tuzhi', 'tuzhi_db_user')
    tuzhi_db_password = config.get('tuzhi', 'tuzhi_db_password')
    tuzhi_db_db = config.get('tuzhi', 'tuzhi_db_db')
    # 临时解压目录，谨慎填写，否则可能会删除正常文件
    zipTempDir = config.get('path', 'zipTempDir')
    downDir = config.get('path', 'downDir')

    access_key = config.get('qiniu', 'access_key')
    secret_key = config.get('qiniu', 'secret_key')
    bucket_name = config.get('qiniu', 'bucket_name')
    bucket_domain = config.get('qiniu', 'bucket_domain')

    cos_access_key = config.get('cos', 'access_key')
    cos_secret_key = config.get('cos', 'secret_key')
    cos_bucket_name = config.get('cos', 'bucket_name')
except:
    print('读取配置文件错误！')
    exit()

def mysqls():
    n = MySQL.MySQL(tuzhi_db_host, tuzhi_user, tuzhi_db_password, 3306)
    n.selectDb(tuzhi_db_db)
    return n

def zip_mysqls():
    n = MySQL.MySQL(zip_db_host, zip_user, zip_db_password, 3306)
    n.selectDb(zip_db_db)
    return n

def mfcad_Lite():
    mfcad_lite = Lite.Lite()
    return mfcad_lite


#修改图纸数据表字段状态
def update_isedrawings(status, tuid):
    n = mysqls()
    n.update('v9_dede_soft', {'isedrawings': status}, 'id=' + str(tuid))
    n.commit()

#添加轻量化数据
def update_task(data, tuid):
    z = zip_mysqls()
    for d in data:
        liteurl_array = base64.b64decode(d['outputNdsFiles']).decode('utf-8').split('?X-Amz-Algorithm')[0].split('/')
        liteurl = 'D:/3DLite/minioSever/severData/openapi/tempFile/download/'+ liteurl_array[-2]+'/'+liteurl_array[-1]
        d['outputNdsFiles'] = '2025_5_10/'+str(tuid)+'/'+str(d['taskId'])+'/'
        file_result = cos_unzip_upload(cos_access_key,cos_secret_key,cos_bucket_name, zipTempDir, liteurl, d['outputNdsFiles'])
        if file_result == False:
            d['outputNdsFiles'] = '上传失败,请检查程序是否异常!'

        imgurl = 'D:/3DLite/minioSever/severData/openapi/' + base64.b64decode(d['imgURL']).decode('utf-8').split('/openapi/')[-1]
        d['imgURL'] = '2025_5_10//'+str(tuid)+'/'+str(d['taskId'])+'/'+str(d['taskId'])+'.png'
        file_result = cos_upload(cos_access_key,cos_secret_key,cos_bucket_name,imgurl, d['imgURL'])
        if file_result == False:
            d['imgURL'] = '上传失败,请检查程序是否异常!'


        z.update('3d_taskdetail',{'inputFileName':d['inputFileName'],'convertTime':d['convertTime'],'inputFileSize':d['inputFileSize'],'outputFileSize':d['outputFileSize'],'convertPercent':d['convertPercent'],'triangleNum':d['triangleNum'],'taskType':d['taskType'],'status':d['status'],'imgURL':d['imgURL'],'outputNdsFiles':d['outputNdsFiles'],'errorDescription':d['errorDescription'],'errorDetail':d['errorDetail'],'errorType':d['errorType']}, 'taskid = ' + str(d['taskId']))
    z.commit()



# 列出符合条件的所有文件，并下载
def listAll(bucket_name):
    n = mysqls()
    z = zip_mysqls()
    mfcad_lite = mfcad_Lite()
    #3天前的时间戳 只处理3天以内的数据
    maxtime = get_unixtime()-(86400*3)
    z.query('SELECT id,GROUP_CONCAT(taskid) as taskid,tuid,`status` FROM `3d_taskdetail` where status = 1 and addtime >= '+ str(maxtime)+' group by tuid order by id asc limit 10')
    tasklist = z.fetchAll()
    
    for tudata in tasklist:
        tasks_success = 3
        all_task_details = []  # 存储所有任务的详细信息
        taskids = tudata['taskid'].split(',')

        # 任务状态缓存和分类
        task_status_cache = {}
        completed_tasks = []  # 已完成的任务
        processing_tasks = []  # 转换中的任务
        max_wait_cycles = 30  # 最大等待轮次(约1分钟)

        print(f'开始处理tuid={tudata["tuid"]}的任务组，包含{len(taskids)}个任务')

        # 第一轮：快速检查所有任务状态，分离已完成和转换中的任务
        for taskid in taskids:
            try:
                taskinfo = mfcad_lite.QueryTaskDetail(taskid)
                taskinfo['data']['taskId'] = taskid
                task_status = int(taskinfo['data']['status'])
                task_status_cache[taskid] = {
                    'info': taskinfo['data'],
                    'status': task_status,
                    'wait_cycles': 0
                }

                if task_status < 2:
                    processing_tasks.append(taskid)
                    print(f'任务{taskid}转换中，状态：{task_status}')
                elif task_status == 2 and taskinfo['data']['imgURL'] != '':
                    completed_tasks.append(taskid)
                    print(f'任务{taskid}已完成')
                elif task_status == 2 and taskinfo['data']['imgURL'] == '':
                    processing_tasks.append(taskid)  # imgURL未返回，仍需等待
                    print(f'任务{taskid}转换完成但imgURL未返回')
                elif task_status > 3:
                    completed_tasks.append(taskid)
                    print(f'任务{taskid}转换失败，状态：{task_status}')
                else:
                    processing_tasks.append(taskid)
                    print(f'任务{taskid}状态异常，状态：{task_status}')
            except Exception as e:
                print(f'查询任务{taskid}状态失败：{e}')
                continue

        # 第二轮：优先处理已完成的任务
        for taskid in completed_tasks:
            task_data = task_status_cache[taskid]['info']
            task_status = task_status_cache[taskid]['status']

            if task_status == 2:
                print(f'处理已完成任务{taskid}')
                tasks_success = 2  # 任何一个任务成功，整体标记为成功
                all_task_details.append(task_data)
            elif task_status > 3:
                print(f'处理失败任务{taskid}，状态码：{task_status}')
                all_task_details.append(task_data)

        # 第三轮：等待转换中的任务，但设置超时机制
        while processing_tasks and max_wait_cycles > 0:
            newly_completed = []

            for taskid in processing_tasks[:]:  # 使用切片复制避免修改迭代中的列表
                try:
                    taskinfo = mfcad_lite.QueryTaskDetail(taskid)
                    taskinfo['data']['taskId'] = taskid
                    task_status = int(taskinfo['data']['status'])

                    # 更新缓存
                    task_status_cache[taskid]['info'] = taskinfo['data']
                    task_status_cache[taskid]['status'] = task_status
                    task_status_cache[taskid]['wait_cycles'] += 1

                    if task_status < 2:
                        print(f'任务{taskid}仍在转换中，已等待{task_status_cache[taskid]["wait_cycles"]}轮')
                    elif task_status == 2 and taskinfo['data']['imgURL'] != '':
                        print(f'任务{taskid}转换成功')
                        tasks_success = 2
                        all_task_details.append(taskinfo['data'])
                        newly_completed.append(taskid)
                    elif task_status > 3:
                        print(f'任务{taskid}转换失败，状态码：{task_status}')
                        all_task_details.append(taskinfo['data'])
                        newly_completed.append(taskid)
                    elif task_status == 2 and taskinfo['data']['imgURL'] == '':
                        print(f'任务{taskid}转换完成但imgURL未返回，继续等待')

                except Exception as e:
                    print(f'查询任务{taskid}状态失败：{e}')
                    newly_completed.append(taskid)  # 查询失败的任务移除，避免无限循环

            # 移除已完成的任务
            for taskid in newly_completed:
                processing_tasks.remove(taskid)

            if processing_tasks:
                print(f'还有{len(processing_tasks)}个任务未完成，等待2秒后继续检查...')
                time.sleep(2)
                max_wait_cycles -= 1

        # 处理超时的任务
        if processing_tasks:
            print(f'以下任务等待超时，跳过处理：{processing_tasks}')
            for taskid in processing_tasks:
                # 记录超时任务的当前状态
                if taskid in task_status_cache:
                    task_data = task_status_cache[taskid]['info']
                    print(f'超时任务{taskid}最后状态：{task_status_cache[taskid]["status"]}')
                    # 可以选择是否将超时任务也加入结果中
                    # all_task_details.append(task_data)

        # 所有任务处理完成后，统一更新数据库状态
        print(f'tuid={tudata["tuid"]}任务组处理完成，成功处理{len(all_task_details)}个任务，最终状态：{tasks_success}')
        update_isedrawings(tasks_success, tudata['tuid'])

        # 更新所有任务的详细信息
        if all_task_details:
            update_task(all_task_details, tudata['tuid'])



if __name__ == '__main__':
    while True:
        listAll(bucket_name)
        print('30秒钟后重新开始扫描！')
        time.sleep(30)
