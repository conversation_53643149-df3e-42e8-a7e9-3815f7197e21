import requests
import datetime
import hashlib
import time


class Lite:
    def __init__(self, ):
        self.api_url = 'http://192.168.0.121:8090'
        self.partner_id = "22709532999846041135"  # 替换为实际的企业ID
        self.partner_key = "46293706021432001649295948959698"  # 替换为实际的密钥
    
        self.api_key, self.timestamp = self.generate_api_key(self.partner_id, self.partner_key)
        self.headers = {
            "timeStampData": self.timestamp,
            "partnerID": self.partner_id,
            "apiKeyData": self.api_key,
        }


    def datetime_to_unix_timestamp(self, dt=None):
        """
        将datetime对象转换为Unix时间戳（秒）
        如果不提供dt参数，则使用当前时间
        
        Args:
            dt: datetime对象，默认为None（使用当前时间）
            
        Returns:
            Unix时间戳（整数，秒）
        """
        if dt is None:
            dt = datetime.datetime.now()
        
        # 获取本地时区偏移
        local_tz_offset = datetime.datetime.now().astimezone().utcoffset().total_seconds()
        
        # 创建1970年1月1日的datetime，并加上本地时区偏移
        start_time = datetime.datetime(1970, 1, 1) + datetime.timedelta(seconds=local_tz_offset)
        
        # 计算时间差（秒）
        return int((dt - start_time).total_seconds())

    def md5(self,msg):
        """
        对输入字符串进行MD5加密，并返回中间16位字符
        
        Args:
            msg: 要加密的字符串
            
        Returns:
            MD5加密后的字符串（取中间16位）
        """
        # 使用UTF-8编码将字符串转换为字节
        buffer = msg.encode('utf-8')
        
        # 计算MD5哈希值
        md5_hash = hashlib.md5(buffer).hexdigest().lower()
        
        # 如果长度大于16，则返回中间16位字符
        if len(md5_hash) > 16:
            return md5_hash[8:24]
        else:
            return md5_hash

    def generate_api_key(self,partner_id,partner_key):
        """
        生成API密钥
        
        Args:
            partner_id: 企业ID
            partner_key: 密钥
            
        Returns:
            生成的API密钥和使用的时间戳
        """
        # 生成时间戳
        timestamp = str(self.datetime_to_unix_timestamp())
        
        # 生成API密钥
        api_key = self.md5(partner_id + partner_key + timestamp)
        
        return api_key, timestamp

    def QueryTask(self,taskID):
        """
        单任务查询接口
        
        Args:
            taskID: 任务ID
        """

        url = self.api_url+"/nds_requests/common/QueryTask"

        response = requests.post( url, data={'taskID': taskID}, headers=self.headers)

        return response.json()


    def QueryTaskDetail(self,taskID):
        """
        单任务查询接口 查询任务详细信息
        
        Args:
            taskID: 任务ID
        """

        url = self.api_url+"/nds_requests/common/QueryTaskDetail"

        response = requests.post( url, data={'taskID': taskID}, headers=self.headers)

        return response.json()

    def QueryBatchTask(self,batchNO):
        """
        批次查询接口
        
        Args:
            batchNO: 批次ID
        """

        url = self.api_url+"/nds_requests/common/QueryBatchTask"

        response = requests.post( url, data={'batchNO': batchNO}, headers=self.headers)

        return response.json()
        

    def QueryTaskModelInfo(self,taskID):
        """
        查询3D模型详细信息
        
        Args:
            taskID: 任务ID
        """

        url = self.api_url+"/nds_requests/common/QueryTaskModelInfo"

        response = requests.post( url, data={'taskID': taskID}, headers=self.headers)

        return response.json()


    def QueryTaskModelFeatures(self,taskID):
        """
        查询对应轻量化文件模型中特征信息
        
        Args:
            taskID: 任务ID
        """

        url = self.api_url+"/nds_requests/common/QueryTaskModelFeatures"

        response = requests.post( url, data={'taskID': taskID}, headers=self.headers)

        return response.json()




    def InsertTask(self,inputURL=None,mainFile=None,returnURL=None,priority=0):
        """
        单任务添加接口
        
        Args:
            inputURL: 提供文件下载地址
            mainFile: 如果上传的是zip文件，不能为空，请填写需要转换的文件名称，装配体文件填写主装文件名称，其他情况可为空
            returnURL:任务处理完成后的回调
            priority: 默认为0，值越大，优先级越高
        """

        url = self.api_url+"/nds_requests/common/InsertTask"

        if not inputURL:
            return "inputURL参数不能为空"
        
        data = {
            "inputURL": inputURL,
        }

        if ('.zip' in inputURL or '.rar' in inputURL or '.7z' in inputURL) and mainFile:
            data["mainFile"] = mainFile
        
        if returnURL:
            data["returnURL"] = returnURL
        
        if priority:
            data["priority"] = priority


        response = requests.post( url, data=data, headers=self.headers)

        return response.json()
    

    def BatchInsertTask(self,inputURL=None,priority=0):
        """
        单任务添加接口
        
        Args:
            inputURL: 提供文件下载地址
            mainFile: 如果上传的是zip文件，不能为空，请填写需要转换的文件名称，装配体文件填写主装文件名称，其他情况可为空
            returnURL:任务处理完成后的回调
            priority: 默认为0，值越大，优先级越高
        """

        url = self.api_url+"/nds_requests/common/BatchInsertTask"

        if not inputURL:
            return "inputURL参数不能为空"
        
        data = {
            "inputURL": inputURL,
        }
        
        if priority:
            data["priority"] = priority


        response = requests.post( url, data=data, headers=self.headers)

        return response.json()


    def InsertHtmlConvertTask(self,inputFiles=None,inputType=1,viewerType=1):
        """
        插入离线预览html转换任务接口
        
        Args:
            inputFiles: 输入文件压缩包（轻量化数据及批注文件压缩包）
            inputType: 文件路径类型 
                0: minio
                1: http路径
                2: ftp路径
            viewerType:轻量化预览类型
                1: 3D轻量化
                2: 2D轻量化
                3: 自定义
                默认1
        """
        url = self.api_url+"/nds_requests/common/InsertHtmlConvertTask"
        if not inputFiles:
            return "inputFiles参数不能为空"
        data = {
            "inputFiles": inputFiles,
            "inputType": inputType,
            "viewerType": viewerType,
        }


        response = requests.post( url, data=data, headers=self.headers)

        return response.json()

# # 示例用法
# if __name__ == "__main__":

#     partner_id = "22709532999846041135"  # 替换为实际的企业ID
#     partner_key = "46293706021432001649295948959698"  # 替换为实际的密钥
    
#     api_key, timestamp = generate_api_key(partner_id, partner_key)
#     print(f"时间戳: {timestamp}")
#     print(f"API密钥: {api_key}")

#     headers = {
#         "timeStampData": timestamp,
#         "partnerID": partner_id,
#         "apiKeyData": api_key,
#     }
    
#     # QueryTask(67)
#     InsertTask()
#     exit()

